* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    margin-top: 20px;
    margin-bottom: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* 顶部导航栏 */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #e0e0e0;
    margin-bottom: 30px;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.header-left i {
    color: #667eea;
    font-size: 20px;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.header-right i {
    font-size: 18px;
    color: #666;
    cursor: pointer;
    transition: color 0.3s;
}

.header-right i:hover {
    color: #667eea;
}

.user-avatar {
    width: 35px;
    height: 35px;
    background: #667eea;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
}

/* 主要内容区域 */
.main-content {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 30px;
}

/* 左侧用户信息卡片 */
.user-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    text-align: center;
    height: fit-content;
}

.user-avatar-large {
    width: 80px;
    height: 80px;
    margin: 0 auto 15px;
    border-radius: 50%;
    overflow: hidden;
}

.user-avatar-large img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-name {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 5px;
    color: #333;
}

.user-role {
    color: #667eea;
    font-size: 14px;
    margin-bottom: 5px;
}

.user-info {
    color: #666;
    font-size: 13px;
    margin-bottom: 25px;
}

.lecture-status {
    text-align: left;
    margin-bottom: 25px;
}

.lecture-status h4 {
    font-size: 16px;
    margin-bottom: 15px;
    color: #333;
}

.status-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 15px;
    margin-bottom: 8px;
    border-radius: 8px;
    font-size: 14px;
}

.status-row:first-of-type {
    background: #667eea;
    color: white;
}

.status-row:last-of-type {
    background: #42A5F5;
    color: white;
}

.status-value {
    font-weight: 600;
}

.completion-rate {
    font-size: 12px;
    color: #666;
    text-align: center;
    margin-top: 10px;
}

.quick-actions {
    display: flex;
    gap: 8px;
}

.action-btn {
    flex: 1;
    padding: 10px;
    border: none;
    border-radius: 8px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
    color: white;
}

.action-btn.orange {
    background: #FF7043;
}

.action-btn.green {
    background: #66BB6A;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* 右侧内容区域 */
.right-content {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

/* 统计数据卡片 */
.stats-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    display: flex;
    gap: 20px;
}

.stat-item {
    flex: 1;
    text-align: center;
    padding: 15px;
    border-radius: 10px;
    transition: all 0.3s;
}

.stat-item.highlight {
    background: #667eea;
    color: white;
    position: relative;
}

.stat-item.highlight i {
    position: absolute;
    top: 15px;
    left: 15px;
    font-size: 16px;
}

.stat-number {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 5px;
    color: inherit;
}

.stat-item:not(.highlight) .stat-number {
    color: #667eea;
}

.stat-label {
    font-size: 12px;
    color: inherit;
    opacity: 0.8;
}

/* 应用网格区域 */
.app-sections {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 25px;
}

.app-section {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.section-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #333;
}

.app-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
}

.app-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px 10px;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s;
    text-align: center;
}

.app-item:hover {
    background: #f8f9fa;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.app-item i {
    font-size: 24px;
    margin-bottom: 8px;
}

.app-item span {
    font-size: 12px;
    color: #333;
    font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .app-sections {
        grid-template-columns: 1fr;
    }
    
    .stats-card {
        flex-direction: column;
        gap: 15px;
    }
    
    .app-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .container {
        margin: 10px;
        padding: 15px;
    }
}
