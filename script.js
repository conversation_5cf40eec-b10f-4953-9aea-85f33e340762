// 教育管理系统交互功能
document.addEventListener('DOMContentLoaded', function() {
    
    // 应用项点击事件
    const appItems = document.querySelectorAll('.app-item');
    appItems.forEach(item => {
        item.addEventListener('click', function() {
            const appName = this.querySelector('span').textContent;
            showNotification(`正在打开 ${appName}...`);
            
            // 添加点击动画效果
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    });

    // 快捷操作按钮点击事件
    const actionBtns = document.querySelectorAll('.action-btn');
    actionBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const actionName = this.textContent;
            showNotification(`正在进入 ${actionName}...`);
            
            // 添加点击动画效果
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    });

    // 统计卡片悬停效果
    const statItems = document.querySelectorAll('.stat-item:not(.highlight)');
    statItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.background = '#f8f9fa';
            this.style.transform = 'translateY(-2px)';
        });
        
        item.addEventListener('mouseleave', function() {
            this.style.background = '';
            this.style.transform = '';
        });
    });

    // 顶部导航图标点击事件
    const headerIcons = document.querySelectorAll('.header-right i');
    headerIcons.forEach(icon => {
        icon.addEventListener('click', function() {
            if (this.classList.contains('fa-th')) {
                showNotification('打开应用菜单');
            } else if (this.classList.contains('fa-bell')) {
                showNotification('查看通知消息');
            }
        });
    });

    // 用户头像点击事件
    const userAvatar = document.querySelector('.user-avatar');
    if (userAvatar) {
        userAvatar.addEventListener('click', function() {
            showNotification('打开用户菜单');
        });
    }

    // 动态更新时间（模拟实时数据）
    function updateStats() {
        const statNumbers = document.querySelectorAll('.stat-number');
        statNumbers.forEach((stat, index) => {
            if (index > 0) { // 跳过第一个高亮项
                const currentValue = parseInt(stat.textContent.replace(',', ''));
                // 随机小幅度变化
                const change = Math.floor(Math.random() * 3) - 1;
                const newValue = Math.max(0, currentValue + change);
                stat.textContent = newValue.toLocaleString();
            }
        });
    }

    // 每30秒更新一次统计数据
    setInterval(updateStats, 30000);

    // 添加页面加载动画
    const cards = document.querySelectorAll('.user-card, .stats-card, .app-section');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // 通知提示功能
    function showNotification(message) {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = 'notification';
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #667eea;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            z-index: 1000;
            font-size: 14px;
            font-weight: 500;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        `;

        document.body.appendChild(notification);

        // 显示动画
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // 自动隐藏
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    // 搜索功能（模拟）
    function initSearch() {
        const searchInput = document.createElement('input');
        searchInput.type = 'text';
        searchInput.placeholder = '搜索应用...';
        searchInput.style.cssText = `
            position: absolute;
            top: -50px;
            left: 50%;
            transform: translateX(-50%);
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 20px;
            width: 250px;
            font-size: 14px;
            opacity: 0;
            transition: all 0.3s ease;
        `;

        const container = document.querySelector('.container');
        container.style.position = 'relative';
        container.appendChild(searchInput);

        // 搜索功能
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const appItems = document.querySelectorAll('.app-item');
            
            appItems.forEach(item => {
                const appName = item.querySelector('span').textContent.toLowerCase();
                if (appName.includes(searchTerm)) {
                    item.style.display = 'flex';
                } else {
                    item.style.display = searchTerm ? 'none' : 'flex';
                }
            });
        });
    }

    // 键盘快捷键
    document.addEventListener('keydown', function(e) {
        // Ctrl + K 打开搜索
        if (e.ctrlKey && e.key === 'k') {
            e.preventDefault();
            const searchInput = document.querySelector('input[placeholder="搜索应用..."]');
            if (searchInput) {
                searchInput.style.opacity = '1';
                searchInput.style.top = '10px';
                searchInput.focus();
            }
        }
        
        // ESC 关闭搜索
        if (e.key === 'Escape') {
            const searchInput = document.querySelector('input[placeholder="搜索应用..."]');
            if (searchInput) {
                searchInput.style.opacity = '0';
                searchInput.style.top = '-50px';
                searchInput.value = '';
                // 显示所有应用
                const appItems = document.querySelectorAll('.app-item');
                appItems.forEach(item => {
                    item.style.display = 'flex';
                });
            }
        }
    });

    // 初始化搜索功能
    initSearch();

    console.log('教育管理系统已加载完成');
});
