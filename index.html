<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>山西农业大学软件学院 - 教育管理系统</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- 顶部导航栏 -->
        <header class="header">
            <div class="header-left">
                <i class="fas fa-university"></i>
                <span>山西农业大学软件学院</span>
            </div>
            <div class="header-right">
                <i class="fas fa-th"></i>
                <i class="fas fa-bell"></i>
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 左侧用户信息卡片 -->
            <div class="user-card">
                <div class="user-avatar-large">
                    <img src="https://via.placeholder.com/80x80/4A90E2/FFFFFF?text=张" alt="用户头像">
                </div>
                <h3 class="user-name">张宝宝</h3>
                <p class="user-role">学生</p>
                <p class="user-info">信息处理学院 | 2307班</p>
                
                <div class="lecture-status">
                    <h4>讲座情况</h4>
                    <div class="status-row">
                        <span class="status-label">已完成</span>
                        <span class="status-value">17</span>
                        <span class="status-label">总计</span>
                        <span class="status-value">58</span>
                        <span class="status-label">完成人数</span>
                    </div>
                    <div class="status-row">
                        <span class="status-label">总计</span>
                        <span class="status-value">63</span>
                        <span class="status-label">总计</span>
                        <span class="status-value">297</span>
                        <span class="status-label">完成人数</span>
                    </div>
                    <p class="completion-rate">学员满意度 已完成</p>
                </div>

                <div class="quick-actions">
                    <button class="action-btn orange">学院新闻</button>
                    <button class="action-btn orange">评价管理</button>
                    <button class="action-btn green">任务版</button>
                </div>
            </div>

            <!-- 右侧内容区域 -->
            <div class="right-content">
                <!-- 统计数据卡片 -->
                <div class="stats-card">
                    <div class="stat-item highlight">
                        <i class="fas fa-arrow-right"></i>
                        <div class="stat-number">68</div>
                        <div class="stat-label">课程情况</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">36</div>
                        <div class="stat-label">总课程</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">32</div>
                        <div class="stat-label">未修完课程</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">2,800</div>
                        <div class="stat-label">毕业应修课学分</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">2,100</div>
                        <div class="stat-label">当前已修学分</div>
                    </div>
                </div>

                <!-- 应用网格区域 -->
                <div class="app-sections">
                    <!-- 智慧教学应用 -->
                    <div class="app-section">
                        <h4 class="section-title">智慧教学应用</h4>
                        <div class="app-grid">
                            <div class="app-item">
                                <i class="fas fa-graduation-cap" style="color: #FFA726;"></i>
                                <span>完善学分制系统</span>
                            </div>
                            <div class="app-item">
                                <i class="fas fa-comments" style="color: #42A5F5;"></i>
                                <span>毕业管理</span>
                            </div>
                            <div class="app-item">
                                <i class="fas fa-file-alt" style="color: #5C6BC0;"></i>
                                <span>实习管理</span>
                            </div>
                            <div class="app-item">
                                <i class="fas fa-cogs" style="color: #26C6DA;"></i>
                                <span>物联网管理</span>
                            </div>
                            <div class="app-item">
                                <i class="fas fa-chart-bar" style="color: #EF5350;"></i>
                                <span>课件共享</span>
                            </div>
                            <div class="app-item">
                                <i class="fas fa-users" style="color: #66BB6A;"></i>
                                <span>教学资源共享</span>
                            </div>
                            <div class="app-item">
                                <i class="fas fa-file-text" style="color: #FF7043;"></i>
                                <span>在线文档</span>
                            </div>
                            <div class="app-item">
                                <i class="fas fa-envelope" style="color: #42A5F5;"></i>
                                <span>电子邮件</span>
                            </div>
                            <div class="app-item">
                                <i class="fas fa-cloud" style="color: #29B6F6;"></i>
                                <span>云盘</span>
                            </div>
                        </div>
                    </div>

                    <!-- AI实验平台 -->
                    <div class="app-section">
                        <h4 class="section-title">AI实验平台</h4>
                        <div class="app-grid">
                            <div class="app-item">
                                <i class="fas fa-snowflake" style="color: #42A5F5;"></i>
                                <span>通用算力平台</span>
                            </div>
                            <div class="app-item">
                                <i class="fas fa-book" style="color: #FF7043;"></i>
                                <span>AI学习资源</span>
                            </div>
                            <div class="app-item">
                                <i class="fas fa-flask" style="color: #66BB6A;"></i>
                                <span>我的AI实验</span>
                            </div>
                            <div class="app-item">
                                <i class="fas fa-robot" style="color: #26C6DA;"></i>
                                <span>AI人机机器人</span>
                            </div>
                            <div class="app-item">
                                <i class="fas fa-star" style="color: #FFA726;"></i>
                                <span>实验平台管理</span>
                            </div>
                            <div class="app-item">
                                <i class="fas fa-code" style="color: #AB47BC;"></i>
                                <span>AI模型SDK</span>
                            </div>
                            <div class="app-item">
                                <i class="fas fa-calendar-check" style="color: #66BB6A;"></i>
                                <span>课程安排</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="script.js"></script>
</body>
</html>
